import 'dart:io';
import '../models/solution/go_model.dart';

class GoModelToTextConverter {
  static String convertToText(GoModel goModel, String tenantName) {
    StringBuffer buffer = StringBuffer();

    // Header - Global Objective Name
    buffer.writeln(
        ' ${_cleanText(goModel.globalObjectives?.name ?? "Global Objective")}');
    buffer.writeln();

    // Core Metadata
    _writeCoreMetadata(buffer, goModel.globalObjectives, tenantName);

    // Process Ownership
    _writeProcessOwnership(buffer, goModel.processOwnership);

    // Trigger Definition
    _writeTriggerDefinition(buffer, goModel.triggerDefinition);

    // Local Objectives
    _writeLocalObjectives(buffer, goModel.localObjectivesList);

    // Pathway Definitions - Generated from Local Objectives
    _writePathwayDefinitions(buffer, goModel.localObjectivesList);

    // Pathways (detailed) - Generated from Local Objectives
    _writeDetailedPathways(buffer, goModel.localObjectivesList);

    // Business Rules
    _writeBusinessRules(
        buffer, goModel.validationRules?.rules, goModel.globalObjectives?.name);

    // Performance Metadata
    _writePerformanceMetadata(buffer, goModel.performanceMetadata);

    // Process Mining Schema
    _writeProcessMiningSchema(buffer, goModel.processMiningSchema);

    // Conformance Analytics
    _writeConformanceAnalytics(buffer, goModel.conformanceAnalytics);

    // Advanced Process Intelligence
    _writeAdvancedProcessIntelligence(
        buffer, goModel.advancedProcessIntelligence);

    // Validation Rules for GO Creation
    _writeValidationRules(buffer, goModel.validationRules?.rules);

    return buffer.toString();
  }

  /// Helper method to clean text and prevent escape character issues
  static String _cleanText(String? text) {
    if (text == null || text.isEmpty) return "";

    // Remove any problematic characters that might cause escape issues
    return text
        .replaceAll('\\', '') // Remove backslashes
        .replaceAll('"', '') // Remove double quotes
        .replaceAll("'", '') // Remove single quotes
        .trim();
  }

  static void _writeCoreMetadata(StringBuffer buffer,
      GlobalObjectives? globalObjectives, String tenantName) {
    buffer.writeln(' Core Metadata:');
    buffer.writeln('- name: ${_cleanText(globalObjectives?.name)}');
    buffer.writeln(
        '- version: ${_cleanText(globalObjectives?.version ?? "0.1")}');
    buffer.writeln(
        '- status: ${_cleanText(globalObjectives?.status ?? "Active")}');
    buffer.writeln(
        '- agent_type: ${_cleanText(globalObjectives?.agentType ?? "HUMAN")}');
    buffer
        .writeln('- description: ${_cleanText(globalObjectives?.description)}');
    buffer.writeln(
        '- primary_entity: ${_cleanText(globalObjectives?.primaryEntity)}');
    buffer.writeln(
        '- classification: ${_cleanText(globalObjectives?.classification)}');
    buffer
        .writeln('  - Global Objective: ${_cleanText(globalObjectives?.name)}');
    buffer.writeln('  - Book: ${_cleanText(globalObjectives?.bookName)}');
    buffer.writeln('  - Chapter: ${_cleanText(globalObjectives?.chapterName)}');
    buffer.writeln(
        '  - Tenant: ${_cleanText(globalObjectives?.tenantName ?? tenantName)}');
    buffer.writeln();
  }

  static void _writeProcessOwnership(
      StringBuffer buffer, ProcessOwnership? processOwnership) {
    buffer.writeln(' Process Ownership:');
    buffer.writeln('- Originator: ${processOwnership?.originator ?? ""}');
    buffer.writeln('- Process Owner: ${processOwnership?.processOwner ?? ""}');
    buffer.writeln(
        '- Business Sponsor: ${processOwnership?.businessSponsor ?? ""}');
    buffer.writeln();
  }

  static void _writeTriggerDefinition(
      StringBuffer buffer, TriggerDefinition? triggerDefinition) {
    buffer.writeln(' Trigger Definition:');
    buffer.writeln('- Trigger Type: ${triggerDefinition?.triggerType ?? ""}');
    buffer.writeln(
        '- Trigger Condition: ${triggerDefinition?.triggerCondition ?? ""}');
    buffer.writeln(
        '- Trigger Schedule: ${triggerDefinition?.triggerSchedule ?? ""}');

    if (triggerDefinition?.triggerAttributes != null &&
        triggerDefinition!.triggerAttributes!.isNotEmpty) {
      buffer.write('- Trigger Attributes: ');
      buffer.writeln(triggerDefinition.triggerAttributes!.join(', '));
    }
    buffer.writeln();
  }

  static void _writeLocalObjectives(
      StringBuffer buffer, List<LocalObjectivesList>? localObjectivesList) {
    buffer.writeln(' Local Objectives:');

    if (localObjectivesList != null) {
      int index = 0;
      for (var lo in localObjectivesList) {
        ++index;
        buffer.writeln(
            'LO-${lo.loNumber ?? index}: ${lo.name} ${lo.agentType ?? "HUMAN"}');
      }
    }
    buffer.writeln();
    buffer.writeln();
  }

  static void _writePathwayDefinitions(
      StringBuffer buffer, List<LocalObjectivesList>? localObjectivesList) {
    buffer.writeln(' Pathway Definitions:');
    buffer.writeln();

    if (localObjectivesList != null && localObjectivesList.isNotEmpty) {
      // Generate pathway definitions using enhanced logic from provider
      List<String> allPossiblePaths =
          _generateAllPossiblePathways(localObjectivesList);

      // Also generate intelligent pathway definitions for pathway names
      List<PathwayDefinition> pathwayDefinitions =
          _generateIntelligentPathwayDefinitions(localObjectivesList);

      // If we have more complex pathways from the new logic, use those
      if (allPossiblePaths.isNotEmpty) {
        for (int i = 0; i < allPossiblePaths.length; i++) {
          String pathwayName = 'Main Workflow';
          if (i < pathwayDefinitions.length) {
            pathwayName =
                pathwayDefinitions[i].pathwayName ?? 'Pathway ${i + 1}';
          } else {
            pathwayName = 'Alternative Flow ${i + 1}';
          }

          buffer.writeln('PATHWAY-${i + 1}: $pathwayName');
          buffer.writeln('${allPossiblePaths[i]}');
          buffer.writeln();
        }
      } else {
        // Fallback to original logic if no complex pathways found
        for (int i = 0; i < pathwayDefinitions.length; i++) {
          var pathway = pathwayDefinitions[i];
          buffer.writeln('PATHWAY-${i + 1}: ${pathway.pathwayName}');

          if (pathway.steps != null && pathway.steps!.isNotEmpty) {
            buffer.writeln('STEPS: ${pathway.steps!.join(' → ')}');
          }
          buffer.writeln();
        }
      }
    }
    buffer.writeln();
  }

  static List<PathwayDefinition> _generateIntelligentPathwayDefinitions(
      List<LocalObjectivesList> localObjectivesList) {
    List<PathwayDefinition> pathways = [];

    // Check if all LOs are sequential
    bool allSequential = _areAllLosSequential(localObjectivesList);

    if (allSequential) {
      // Generate one main pathway for all sequential LOs
      String mainPathwayName = _generateMainWorkflowName(localObjectivesList);
      List<String> steps = _generateSequentialSteps(localObjectivesList);

      pathways.add(PathwayDefinition(
        pathwayName: mainPathwayName,
        steps: steps,
      ));
    } else {
      // Generate separate pathways for different types
      pathways.addAll(_generateSeparatePathways(localObjectivesList));
    }

    return pathways;
  }

  static bool _areAllLosSequential(
      List<LocalObjectivesList> localObjectivesList) {
    for (var lo in localObjectivesList) {
      if (lo.pathwayData != null) {
        // Check if it has alternative or parallel data
        if (lo.pathwayData!.alternativeData != null &&
            lo.pathwayData!.alternativeData!.pathwayEntries.isNotEmpty) {
          return false;
        }

        if (lo.pathwayData!.parallelData != null &&
            lo.pathwayData!.parallelData!.pathwayEntries.isNotEmpty) {
          return false;
        }

        if (lo.pathwayData!.recursiveData?.isRecursive == true) {
          return false;
        }

        // Check if it's terminal (end of process)
        if (lo.pathwayData!.isTerminal == true) {
          // Terminal is still considered sequential for the main flow
          continue;
        }
      }
    }
    return true;
  }

  static String _generateMainWorkflowName(
      List<LocalObjectivesList> localObjectivesList) {
    if (localObjectivesList.isEmpty) return 'Main Workflow';

    // Extract entity name from first LO
    String entityName =
        _extractEntityName(localObjectivesList.first.name ?? 'Process');
    return '$entityName Main Workflow';
  }

  static List<String> _generateSequentialSteps(
      List<LocalObjectivesList> localObjectivesList) {
    List<String> steps = [];

    for (int i = 0; i < localObjectivesList.length; i++) {
      var lo = localObjectivesList[i];
      String loNumber = 'LO-${lo.loNumber ?? (i + 1)}';
      steps.add(loNumber);
    }

    return steps;
  }

  static List<PathwayDefinition> _generateSeparatePathways(
      List<LocalObjectivesList> localObjectivesList) {
    List<PathwayDefinition> pathways = [];

    // First, create a main sequential pathway for LOs without special pathway data
    List<LocalObjectivesList> sequentialLos = [];
    List<LocalObjectivesList> specialPathwayLos = [];

    for (var lo in localObjectivesList) {
      if (_hasSpecialPathwayData(lo)) {
        specialPathwayLos.add(lo);
      } else {
        sequentialLos.add(lo);
      }
    }

    // Add main sequential pathway if there are sequential LOs
    if (sequentialLos.isNotEmpty) {
      String mainPathwayName = _generateMainWorkflowName(sequentialLos);
      List<String> steps = _generateSequentialSteps(sequentialLos);

      pathways.add(PathwayDefinition(
        pathwayName: mainPathwayName,
        steps: steps,
      ));
    }

    // Group special pathway LOs by type and create separate pathways
    Map<String, List<LocalObjectivesList>> pathwayGroups = {};

    for (var lo in specialPathwayLos) {
      String pathwayType = _determinePathwayType(lo);

      if (!pathwayGroups.containsKey(pathwayType)) {
        pathwayGroups[pathwayType] = [];
      }
      pathwayGroups[pathwayType]!.add(lo);
    }

    // Generate pathway for each type
    pathwayGroups.forEach((pathwayType, los) {
      String pathwayName = _generatePathwayNameByType(pathwayType, los);
      List<String> steps =
          _generateStepsByPathwayType(pathwayType, los, localObjectivesList);

      pathways.add(PathwayDefinition(
        pathwayName: pathwayName,
        steps: steps,
      ));
    });

    return pathways;
  }

  static bool _hasSpecialPathwayData(LocalObjectivesList lo) {
    if (lo.pathwayData != null) {
      return (lo.pathwayData!.alternativeData != null &&
              lo.pathwayData!.alternativeData!.pathwayEntries.isNotEmpty) ||
          (lo.pathwayData!.parallelData != null &&
              lo.pathwayData!.parallelData!.pathwayEntries.isNotEmpty) ||
          (lo.pathwayData!.recursiveData?.isRecursive == true);
    }
    return false;
  }

  static String _extractEntityName(String text) {
    if (text.isEmpty) return 'Entity';

    // Try to extract entity name from common patterns
    // Remove common prefixes and suffixes
    String cleaned = text
        .replaceAll(
            RegExp(r'^(Create|Update|Delete|Manage|Process|Handle)\s+'), '')
        .replaceAll(RegExp(r'\s+(Process|System|Workflow|Management)$'), '')
        .trim();

    // If cleaned is empty, return original text
    if (cleaned.isEmpty) return text;

    // Capitalize first letter
    return cleaned[0].toUpperCase() + cleaned.substring(1).toLowerCase();
  }

  static String _determinePathwayType(LocalObjectivesList lo) {
    if (lo.pathwayData != null) {
      if (lo.pathwayData!.alternativeData != null &&
          lo.pathwayData!.alternativeData!.pathwayEntries.isNotEmpty) {
        return 'Alternative';
      } else if (lo.pathwayData!.parallelData != null &&
          lo.pathwayData!.parallelData!.pathwayEntries.isNotEmpty) {
        return 'Parallel';
      } else if (lo.pathwayData!.recursiveData?.isRecursive == true) {
        return 'Recursive';
      } else if (lo.pathwayData!.isTerminal == true) {
        return 'Terminal';
      } else if (lo.pathwayData!.sequentialData != null) {
        return 'Sequential';
      }
    }
    return 'Sequential';
  }

  static String _generatePathwayNameByType(
      String pathwayType, List<LocalObjectivesList> los) {
    if (los.isEmpty) return 'Unknown Pathway';

    // Get the first LO name as base
    String baseName = los.first.name ?? 'Process';
    String entityName = _extractEntityName(baseName);

    // If multiple LOs, include count
    String countSuffix = los.length > 1 ? ' (${los.length} LOs)' : '';

    switch (pathwayType) {
      case 'Alternative':
        return '$entityName Alternative Flow$countSuffix';
      case 'Parallel':
        return '$entityName Parallel Processing$countSuffix';
      case 'Recursive':
        return '$entityName Recursive Process$countSuffix';
      case 'Terminal':
        return '$entityName Completion Flow$countSuffix';
      case 'Sequential':
      default:
        return '$entityName Sequential Flow$countSuffix';
    }
  }

  static List<String> _generateStepsByPathwayType(String pathwayType,
      List<LocalObjectivesList> los, List<LocalObjectivesList> allLos) {
    List<String> steps = [];

    switch (pathwayType) {
      case 'Alternative':
        // For alternative pathways, show the branching points
        for (var lo in los) {
          String loNumber = 'LO-${lo.loNumber ?? 0}';
          steps.add(loNumber);
          if (lo.pathwayData?.alternativeData != null) {
            for (var entry in lo.pathwayData!.alternativeData!.pathwayEntries) {
              String targetLoNumber =
                  _findLoNumberById(entry.selectedLO ?? '', allLos);
              steps.add('  → $targetLoNumber');
            }
          }
        }
        break;

      case 'Parallel':
        // For parallel pathways, show parallel execution
        for (var lo in los) {
          String loNumber = 'LO-${lo.loNumber ?? 0}';
          steps.add(loNumber);
          if (lo.pathwayData?.parallelData != null) {
            for (var entry in lo.pathwayData!.parallelData!.pathwayEntries) {
              String targetLoNumber =
                  _findLoNumberById(entry.selectedLO ?? '', allLos);
              steps.add('  || $targetLoNumber');
            }
          }
        }
        break;

      case 'Recursive':
        // For recursive pathways, show the loop
        for (var lo in los) {
          String loNumber = 'LO-${lo.loNumber ?? 0}';
          steps.add(loNumber);
          steps.add('  ↻ Loop back to $loNumber');
        }
        break;

      case 'Terminal':
        // For terminal pathways, show completion
        for (var lo in los) {
          String loNumber = 'LO-${lo.loNumber ?? 0}';
          steps.add(loNumber);
          steps.add('  ✓ Complete');
        }
        break;

      case 'Sequential':
      default:
        // For sequential pathways, show linear flow
        for (int i = 0; i < los.length; i++) {
          var lo = los[i];
          String loNumber = 'LO-${lo.loNumber ?? (i + 1)}';
          steps.add(loNumber);
        }
        break;
    }

    return steps;
  }

  static void _writeDetailedPathways(
      StringBuffer buffer, List<LocalObjectivesList>? localObjectivesList) {
    buffer.writeln(' Pathways:');
    buffer.writeln();

    if (localObjectivesList != null) {
      for (int i = 0; i < localObjectivesList.length; i++) {
        var lo = localObjectivesList[i];
        buffer.writeln('${i + 1}. ${lo.name} ${lo.agentType ?? "HUMAN"}');

        // Generate description from natural language or create one
        String description = lo.naturalLanguage ?? _generateLoDescription(lo);
        buffer.writeln('   Description: $description');

        // Determine route type from pathway data
        String routeType = _determineRouteType(lo, localObjectivesList, i);
        buffer.writeln('   Route Type: $routeType');

        // Generate routes based on pathway data and LO relationships
        List<String> routes = _generateRoutes(lo, localObjectivesList, i);
        for (int j = 0; j < routes.length; j++) {
          buffer.writeln('   ${String.fromCharCode(97 + j)}. ${routes[j]}');
        }

        buffer.writeln();
      }
    }
  }

  static String _generateLoDescription(LocalObjectivesList lo) {
    // Generate description based on LO name and type
    String actorType = lo.agentType?.toLowerCase() ?? 'user';
    String loName = lo.name ?? 'process';

    if (loName.toLowerCase().contains('create')) {
      return '$actorType creates new ${_extractEntityName(loName).toLowerCase()} record with comprehensive validation against ${_extractEntityName(loName).toLowerCase()} profile requirements and organizational standards';
    } else if (loName.toLowerCase().contains('update')) {
      return '$actorType updates existing ${_extractEntityName(loName).toLowerCase()} information with validation and change tracking';
    } else if (loName.toLowerCase().contains('view') ||
        loName.toLowerCase().contains('details')) {
      return '$actorType views detailed ${_extractEntityName(loName).toLowerCase()} information with options for record modification';
    } else if (loName.toLowerCase().contains('delete')) {
      return '$actorType deletes ${_extractEntityName(loName).toLowerCase()} record with proper validation and audit trail';
    }

    return '$actorType performs $loName operation with appropriate validation and processing';
  }

  static String _determineRouteType(LocalObjectivesList lo,
      List<LocalObjectivesList> allLos, int currentIndex) {
    // Determine route type based on pathway data
    if (lo.pathwayData != null) {
      String? selectedType = lo.pathwayData!.selectedType;
      if (selectedType != null) {
        return selectedType;
      }

      // Check specific pathway data types
      if (lo.pathwayData!.alternativeData != null &&
          lo.pathwayData!.alternativeData!.pathwayEntries.isNotEmpty) {
        return 'Alternative';
      } else if (lo.pathwayData!.parallelData != null &&
          lo.pathwayData!.parallelData!.pathwayEntries.isNotEmpty) {
        return 'Parallel';
      } else if (lo.pathwayData!.recursiveData?.isRecursive == true) {
        return 'Recursive';
      } else if (lo.pathwayData!.isTerminal == true) {
        return 'Terminal';
      }
    }

    return 'Sequential'; // Default
  }

  static List<String> _generateRoutes(LocalObjectivesList lo,
      List<LocalObjectivesList> allLos, int currentIndex) {
    List<String> routes = [];

    if (lo.pathwayData != null) {
      var pathwayData = lo.pathwayData!;

      // Handle different pathway types
      if (pathwayData.alternativeData != null &&
          pathwayData.alternativeData!.pathwayEntries.isNotEmpty) {
        // Alternative pathways
        for (var entry in pathwayData.alternativeData!.pathwayEntries) {
          if (entry.selectedLO != null) {
            // Find the actual LO name from the selectedLO reference
            String targetLoName = _findLoNameById(entry.selectedLO!, allLos);
            String route = 'Route to $targetLoName';
            if (entry.condition != null && entry.condition!.isNotEmpty) {
              route += ' (Condition: ${entry.condition})';
            }
            routes.add(route);
          }
        }
      } else if (pathwayData.parallelData != null &&
          pathwayData.parallelData!.pathwayEntries.isNotEmpty) {
        // Parallel pathways
        for (var entry in pathwayData.parallelData!.pathwayEntries) {
          if (entry.selectedLO != null) {
            // Find the actual LO name from the selectedLO reference
            String targetLoName = _findLoNameById(entry.selectedLO!, allLos);
            routes.add('Route to $targetLoName (Parallel)');
          }
        }
      } else if (pathwayData.sequentialData != null &&
          pathwayData.sequentialData!.selectedLO != null) {
        // Sequential pathway
        // Find the actual LO name from the selectedLO reference
        String targetLoName =
            _findLoNameById(pathwayData.sequentialData!.selectedLO!, allLos);
        routes.add('Route to $targetLoName');
      } else if (pathwayData.isTerminal == true) {
        // Terminal pathway
        routes.add('Complete process');
      } else if (pathwayData.recursiveData?.isRecursive == true) {
        // Recursive pathway
        routes.add('Route back to ${lo.name} (Recursive)');
      }
    }

    // If no specific pathway data, generate default sequential route
    if (routes.isEmpty) {
      if (currentIndex < allLos.length - 1) {
        // Route to next LO
        var nextLo = allLos[currentIndex + 1];
        routes.add('Route to ${nextLo.name}');
      } else {
        // Last LO - complete process
        routes.add('Complete process');
      }
    }

    return routes;
  }

  /// Helper method to find LO name by ID or reference
  static String _findLoNameById(
      String selectedLO, List<LocalObjectivesList> allLos) {
    // First, try to find by loId
    for (var lo in allLos) {
      if (lo.loId == selectedLO) {
        return lo.name ?? 'Unknown LO';
      }
    }

    // If not found by loId, try to find by name (in case selectedLO is already a name)
    for (var lo in allLos) {
      if (lo.name == selectedLO) {
        return lo.name ?? 'Unknown LO';
      }
    }

    // If still not found, try to find by loNumber
    try {
      int? loNumber = int.tryParse(selectedLO);
      if (loNumber != null) {
        for (var lo in allLos) {
          if (lo.loNumber == loNumber) {
            return lo.name ?? 'Unknown LO';
          }
        }
      }
    } catch (e) {
      // Ignore parsing errors
    }

    // If nothing found, return the original selectedLO or a default
    return selectedLO.isNotEmpty ? selectedLO : 'Next LO';
  }

  /// Helper method to find LO number by ID or reference
  static String _findLoNumberById(
      String selectedLO, List<LocalObjectivesList> allLos) {
    // First, try to find by loId
    for (var lo in allLos) {
      if (lo.loId == selectedLO) {
        return 'LO-${lo.loNumber ?? 0}';
      }
    }

    // If not found by loId, try to find by name (in case selectedLO is already a name)
    for (var lo in allLos) {
      if (lo.name == selectedLO) {
        return 'LO-${lo.loNumber ?? 0}';
      }
    }

    // If still not found, try to find by loNumber
    try {
      int? loNumber = int.tryParse(selectedLO);
      if (loNumber != null) {
        for (var lo in allLos) {
          if (lo.loNumber == loNumber) {
            return 'LO-${lo.loNumber ?? 0}';
          }
        }
      }
    } catch (e) {
      // Ignore parsing errors
    }

    // If nothing found, return the original selectedLO or a default
    return selectedLO.isNotEmpty ? 'LO-$selectedLO' : 'LO-0';
  }

  static void _writeBusinessRules(
      StringBuffer buffer, List<Rule>? rules, String? goName) {
    buffer.writeln('Business Rules for ${goName ?? ""}:');
    buffer.writeln();

    if (rules != null) {
      for (var rule in rules) {
        buffer.writeln(' ${rule.ruleName}');
        if (rule.ruleInputs != null) {
          buffer.writeln('Inputs: ${rule.ruleInputs!.join('; ')}');
        }
        buffer.writeln('Operation: ${rule.ruleOperation ?? ""}');
        buffer.writeln('Description: ${rule.ruleDescription ?? ""}');
        buffer.writeln('Output: ${rule.ruleOutput ?? ""}');
        if (rule.ruleError != null && rule.ruleError!.isNotEmpty) {
          buffer.writeln('Error: ${rule.ruleError}');
        }
        if (rule.ruleValidation != null && rule.ruleValidation!.isNotEmpty) {
          buffer.writeln('Validation: ${rule.ruleValidation}');
        }
        buffer.writeln();
      }
    }
  }

  static void _writePerformanceMetadata(
      StringBuffer buffer, PerformanceMetadataClass? performanceMetadata) {
    buffer.writeln(' Performance Metadata:');

    if (performanceMetadata?.metadataData != null) {
      var data = performanceMetadata!.metadataData!;

      buffer.writeln('- cycle_time: ${data.cycleTime ?? ""}');
      buffer.writeln('- number_of_pathways: ${data.numberOfPathways ?? 0}');

      if (data.volumeMetrics != null) {
        buffer.writeln('- volume_metrics:');
        buffer.writeln(
            '  * average_volume: ${data.volumeMetrics!.averageVolume ?? 0}');
        buffer
            .writeln('  * peak_volume: ${data.volumeMetrics!.peakVolume ?? 0}');
        buffer.writeln('  * unit: ${data.volumeMetrics!.unit ?? ""}');
      }

      if (data.slaThresholds != null) {
        buffer.writeln('- sla_thresholds:');
        buffer.writeln(
            '  * record_creation: ${data.slaThresholds!.recordCreation ?? ""}');
        buffer.writeln(
            '  * record_update: ${data.slaThresholds!.recordUpdate ?? ""}');
        buffer.writeln(
            '  * record_search: ${data.slaThresholds!.recordSearch ?? ""}');
        buffer.writeln(
            '  * record_deletion: ${data.slaThresholds!.recordDeletion ?? ""}');
      }

      if (data.criticalLoPerformance != null) {
        buffer.writeln('- critical_lo_performance:');
        buffer.writeln(
            '  * "CreateEmployeeRecord": ${data.criticalLoPerformance!.createEmployeeRecord ?? ""}');
        buffer.writeln(
            '  * "UpdateEmployeeRecord": ${data.criticalLoPerformance!.updateEmployeeRecord ?? ""}');
        buffer.writeln(
            '  * "ViewEmployeeDatabase": ${data.criticalLoPerformance!.viewEmployeeDatabase ?? ""}');
        buffer.writeln(
            '  * "DeleteEmployeeRecord": ${data.criticalLoPerformance!.deleteEmployeeRecord ?? ""}');
      }
    }
    buffer.writeln();
  }

  static void _writeProcessMiningSchema(
      StringBuffer buffer, ProcessMiningSchema? processMiningSchema) {
    buffer.writeln('Process Mining Schema:');
    buffer.writeln();

    if (processMiningSchema?.schemaData?.eventLogSpecification != null) {
      var eventLog = processMiningSchema!.schemaData!.eventLogSpecification!;

      buffer.writeln(' Event Log Specification:');
      buffer.writeln('- case_id: ${eventLog.caseId ?? ""}');
      buffer.writeln('- activity: ${eventLog.activity ?? ""}');
      buffer.writeln('- event_type: ${eventLog.eventType ?? ""}');
      buffer.writeln('- timestamp: ${eventLog.timestamp ?? ""}');
      buffer.writeln('- resource: ${eventLog.resource ?? ""}');
      buffer.writeln('- duration: ${eventLog.duration ?? ""}');

      if (eventLog.attributes != null) {
        buffer.writeln('- attributes:');
        buffer.writeln(
            '  * entity_state: ${eventLog.attributes!.entityState ?? ""}');
        buffer.writeln(
            '  * input_values: ${eventLog.attributes!.inputValues ?? ""}');
        buffer.writeln(
            '  * output_values: ${eventLog.attributes!.outputValues ?? ""}');
        buffer.writeln(
            '  * execution_status: ${eventLog.attributes!.executionStatus ?? ""}');
        buffer.writeln(
            '  * error_details: ${eventLog.attributes!.errorDetails ?? ""}');
      }
      buffer.writeln();
    }

    // Performance Discovery Metrics would be added here based on the processMiningSchema data
    _writePerformanceDiscoveryMetrics(
        buffer, processMiningSchema?.performanceDiscoveryMetrics);
  }

  static void _writePerformanceDiscoveryMetrics(StringBuffer buffer,
      ProcessMiningSchemaPerformanceDiscoveryMetrics? metrics) {
    buffer.writeln(' Performance Discovery Metrics:');
    // Add implementation based on your specific metrics structure
    // This would map the pathway frequency, bottleneck analysis, and resource patterns
    buffer.writeln();
  }

  static void _writeConformanceAnalytics(
      StringBuffer buffer, GoModelConformanceAnalytics? conformanceAnalytics) {
    buffer.writeln(' Conformance Analytics:');

    if (conformanceAnalytics != null) {
      buffer.writeln(
          '- compliance_rate: ${conformanceAnalytics.complianceRate ?? 0}');

      // Add execution variance and exception patterns based on your model structure
      if (conformanceAnalytics.exceptionPatterns != null) {
        buffer.writeln('- exception_patterns:');
        // Map your exception patterns here
      }
    }
    buffer.writeln();
  }

  static void _writeAdvancedProcessIntelligence(
      StringBuffer buffer, GoModelAdvancedProcessIntelligence? advancedPI) {
    buffer.writeln(' Advanced Process Intelligence:');

    if (advancedPI?.processHealthScore != null) {
      var healthScore = advancedPI!.processHealthScore!;
      buffer.writeln('- process_health_score:');
      buffer.writeln(
          '  * performance_score: ${healthScore.performanceScore ?? 0}');
      buffer
          .writeln('  * compliance_score: ${healthScore.complianceScore ?? 0}');
      buffer
          .writeln('  * efficiency_score: ${healthScore.efficiencyScore ?? 0}');
      buffer.writeln('  * overall_health: ${healthScore.overallHealth ?? 0}');
    }

    if (advancedPI?.predictionModels?.completionTimeForecast != null) {
      var forecast = advancedPI!.predictionModels!.completionTimeForecast!;
      buffer.writeln('- prediction_models:');
      buffer.writeln('  * completion_time_forecast:');
      buffer.writeln('    - algorithm: ${forecast.algorithm ?? ""}');
      buffer.writeln('    - accuracy: ${forecast.accuracy ?? 0}');
      buffer.writeln(
          '    - confidence_interval: ${forecast.confidenceInterval ?? ""}');
    }

    if (advancedPI?.optimizationInsights != null) {
      var insights = advancedPI!.optimizationInsights!;
      buffer.writeln('- optimization_insights:');

      if (insights.bottleneckElimination != null) {
        buffer.writeln(
            '  * bottleneck_elimination: ${_formatStringList(insights.bottleneckElimination!)}');
      }
      if (insights.resourceReallocation != null) {
        buffer.writeln(
            '  * resource_reallocation: ${_formatStringList(insights.resourceReallocation!)}');
      }
      if (insights.pathwayOptimization != null) {
        buffer.writeln(
            '  * pathway_optimization: ${_formatStringList(insights.pathwayOptimization!)}');
      }
    }
    buffer.writeln();
  }

  static void _writeValidationRules(StringBuffer buffer, List<Rule>? rules) {
    buffer.writeln(' Validation Rules for GO Creation:');
    buffer.writeln();

    if (rules != null) {
      for (var rule in rules
          .where((r) => r.ruleValidation?.contains('PRE_DEPLOY') == true)) {
        buffer.writeln(' ${rule.ruleName}');
        if (rule.ruleInputs != null) {
          buffer.writeln('Inputs: ${rule.ruleInputs!.join(', ')}');
        }
        buffer.writeln('Operation: ${rule.ruleOperation ?? ""}');
        buffer.writeln('Description: ${rule.ruleDescription ?? ""}');
        buffer.writeln('Output: ${rule.ruleOutput ?? ""}');
        if (rule.ruleError != null) {
          buffer.writeln('Error: ${rule.ruleError}');
        }
        if (rule.ruleValidation != null) {
          buffer.writeln('Validation: ${rule.ruleValidation}');
        }
        buffer.writeln();
      }
    }
  }

  // Helper methods

  static String _formatStringList(List<String> list) {
    return '["${list.join('", "')}"]';
  }

  // Method to save to file
  static Future<void> saveToFile(
      GoModel goModel, String filePath, String tenantName) async {
    final textContent = convertToText(goModel, tenantName);
    final file = File(filePath);
    await file.writeAsString(textContent);
    print('GoModel converted and saved to: $filePath');
  }

  /// Test method to demonstrate the converter working
  static void testConverter() {
    // Create a sample GoModel for testing
    // Expected output format:
    // PATHWAY-1: Employee Main Workflow
    // STEPS: LO-1 → LO-2
    //
    // PATHWAY-2: Employee Alternative Flow (1 LOs)
    // STEPS: LO-3
    //   → LO-4
    //   → LO-5
    //
    // PATHWAY-3: Employee Parallel Processing (1 LOs)
    // STEPS: LO-4
    //   || LO-6
    //   || LO-7
    //   || LO-8

    final testGoModel = GoModel(
      globalObjectives: GlobalObjectives(
        name: 'Employee Management System',
        version: '1.0',
        status: 'Active',
        description: 'Comprehensive employee management process',
        primaryEntity: 'Employee',
        classification: 'HR Process',
        bookName: 'HR Handbook',
        chapterName: 'Employee Management',
        tenantName: 'Test Company',
      ),
      processOwnership: ProcessOwnership(
        originator: 'HR Manager',
        processOwner: 'HR Director',
        businessSponsor: 'CEO',
      ),
      localObjectivesList: [
        // Sequential LOs
        LocalObjectivesList(
          name: 'Create Employee Profile',
          loNumber: 1,
          agentType: 'HUMAN',
        ),
        LocalObjectivesList(
          name: 'Assign Department',
          loNumber: 2,
          agentType: 'HUMAN',
        ),
        // Alternative pathway LO
        LocalObjectivesList(
          name: 'Review Application',
          loNumber: 3,
          agentType: 'HUMAN',
          pathwayData: PathwayData(
            alternativeData: AlternativePathwayData(
              pathwayEntries: [
                PathwayEntry(selectedLO: 'Approve Application'),
                PathwayEntry(selectedLO: 'Reject Application'),
              ],
            ),
          ),
        ),
        // Parallel pathway LO
        LocalObjectivesList(
          name: 'Process Documents',
          loNumber: 4,
          agentType: 'SYSTEM',
          pathwayData: PathwayData(
            parallelData: ParallelPathwayData(
              pathwayEntries: [
                PathwayEntry(selectedLO: 'Generate ID Card'),
                PathwayEntry(selectedLO: 'Setup Email Account'),
                PathwayEntry(selectedLO: 'Create Payroll Record'),
              ],
            ),
          ),
        ),
        // Terminal LO
        LocalObjectivesList(
          name: 'Complete Onboarding',
          loNumber: 5,
          agentType: 'HUMAN',
          pathwayData: PathwayData(isTerminal: true),
        ),
      ],
    );

    // Test the converter
    String result = convertToText(testGoModel, 'Test Company');
    print('=== GO Text Converter Test ===');
    print(result);
    print('=== End Test ===');
  }

  static List<String> writePathwayDefinitions(
      List<LocalObjectivesList>? localObjectivesList) {
    List<String> finalPathWays = [];
    if (localObjectivesList != null && localObjectivesList.isNotEmpty) {
      // Generate pathway definitions using enhanced logic from provider
      finalPathWays = _generateAllPossiblePathways(localObjectivesList);
    }
    return finalPathWays;
  }

  /// Generate all possible pathway combinations considering alternatives (from provider logic)
  static List<String> _generateAllPossiblePathways(
      List<LocalObjectivesList> localObjectivesList) {
    if (localObjectivesList.isEmpty) return [];

    // Find all pathways by recursively building combinations
    List<List<String>> allPaths =
        _buildAllPathwayCombinations(localObjectivesList, 0, []);

    // Convert to formatted strings
    List<String> formattedPaths = [];
    for (int i = 0; i < allPaths.length; i++) {
      if (allPaths[i].isNotEmpty) {
        formattedPaths.add('STEPS: ${allPaths[i].join(' → ')}');
      }
    }

    return formattedPaths;
  }

  /// Recursively build all possible pathway combinations (from provider logic)
  static List<List<String>> _buildAllPathwayCombinations(
      List<LocalObjectivesList> localObjectivesList,
      int currentLoIndex,
      List<String> currentPath) {
    List<List<String>> allPaths = [];

    // Base case: if we've processed all LOs, return the current path
    if (currentLoIndex >= localObjectivesList.length) {
      return [List.from(currentPath)];
    }

    LocalObjectivesList currentLo = localObjectivesList[currentLoIndex];
    String currentLoNumber = 'LO-${currentLo.loNumber ?? (currentLoIndex + 1)}';

    // Add current LO to path
    List<String> newPath = List.from(currentPath);
    newPath.add(currentLoNumber);

    // Check if this LO has pathway data
    if (currentLo.pathwayData != null) {
      String? pathwayType = currentLo.pathwayData!.selectedType;

      switch (pathwayType) {
        case 'Alternative':
          // Handle alternative pathways - create separate paths for each alternative
          if (currentLo.pathwayData!.alternativeData != null) {
            var alternativeEntries =
                currentLo.pathwayData!.alternativeData!.pathwayEntries;

            if (alternativeEntries.isNotEmpty) {
              // Group alternatives by their selectedLO
              Map<String?, List<PathwayEntry>> groupedAlternatives = {};

              for (var entry in alternativeEntries) {
                String? targetLo = entry.selectedLO;
                if (targetLo != null && targetLo.isNotEmpty) {
                  if (!groupedAlternatives.containsKey(targetLo)) {
                    groupedAlternatives[targetLo] = [];
                  }
                  groupedAlternatives[targetLo]!.add(entry);
                }
              }

              // Create separate paths for each alternative target
              for (String? targetLo in groupedAlternatives.keys) {
                if (targetLo != null) {
                  // Find the index of the target LO
                  int? targetLoIndex =
                      _findLoIndexByName(localObjectivesList, targetLo);

                  if (targetLoIndex != null &&
                      targetLoIndex < localObjectivesList.length) {
                    // Create path through this alternative
                    List<String> alternativePath = List.from(newPath);

                    // Add target LO
                    String targetLoNumber =
                        'LO-${localObjectivesList[targetLoIndex].loNumber ?? (targetLoIndex + 1)}';
                    alternativePath.add(targetLoNumber);

                    // Continue building path from the target LO's next position
                    List<List<String>> continuedPaths =
                        _buildAllPathwayCombinations(localObjectivesList,
                            targetLoIndex + 1, alternativePath);

                    allPaths.addAll(continuedPaths);
                  }
                }
              }

              // If no valid alternatives found, continue with next LO
              if (allPaths.isEmpty) {
                List<List<String>> continuedPaths =
                    _buildAllPathwayCombinations(
                        localObjectivesList, currentLoIndex + 1, newPath);
                allPaths.addAll(continuedPaths);
              }
            } else {
              // No alternative entries, continue with next LO
              List<List<String>> continuedPaths = _buildAllPathwayCombinations(
                  localObjectivesList, currentLoIndex + 1, newPath);
              allPaths.addAll(continuedPaths);
            }
          }
          break;

        case 'Sequential':
          // Handle sequential pathways
          if (currentLo.pathwayData!.sequentialData?.selectedLO != null) {
            String targetLo =
                currentLo.pathwayData!.sequentialData!.selectedLO!;
            int? targetLoIndex =
                _findLoIndexByName(localObjectivesList, targetLo);

            if (targetLoIndex != null &&
                targetLoIndex < localObjectivesList.length) {
              // Jump to the target LO
              List<List<String>> continuedPaths = _buildAllPathwayCombinations(
                  localObjectivesList, targetLoIndex, newPath);
              allPaths.addAll(continuedPaths);
            } else {
              // Continue with next LO
              List<List<String>> continuedPaths = _buildAllPathwayCombinations(
                  localObjectivesList, currentLoIndex + 1, newPath);
              allPaths.addAll(continuedPaths);
            }
          } else {
            // Continue with next LO
            List<List<String>> continuedPaths = _buildAllPathwayCombinations(
                localObjectivesList, currentLoIndex + 1, newPath);
            allPaths.addAll(continuedPaths);
          }
          break;

        case 'Terminal':
          // Terminal LO - end the path here
          return [newPath];

        case 'Parallel':
          if (currentLo.pathwayData!.parallelData != null) {
            var parallelEntries =
                currentLo.pathwayData!.parallelData!.pathwayEntries;

            if (parallelEntries.isNotEmpty) {
              // Group parallel paths by their selectedLO
              Map<String?, List<PathwayEntry>> groupedParallel = {};

              for (var entry in parallelEntries) {
                String? targetLo = entry.selectedLO;
                if (targetLo != null && targetLo.isNotEmpty) {
                  if (!groupedParallel.containsKey(targetLo)) {
                    groupedParallel[targetLo] = [];
                  }
                  groupedParallel[targetLo]!.add(entry);
                }
              }

              // Create separate paths for each parallel target
              for (String? targetLo in groupedParallel.keys) {
                if (targetLo != null) {
                  // Find the index of the target LO
                  int? targetLoIndex =
                      _findLoIndexByName(localObjectivesList, targetLo);

                  if (targetLoIndex != null &&
                      targetLoIndex < localObjectivesList.length) {
                    // Create path through this parallel route
                    List<String> parallelPath = List.from(newPath);

                    // Add target LO
                    String targetLoNumber =
                        'LO-${localObjectivesList[targetLoIndex].loNumber ?? (targetLoIndex + 1)}';
                    parallelPath.add(targetLoNumber);

                    // Continue building path from the target LO's next position
                    List<List<String>> continuedPaths =
                        _buildAllPathwayCombinations(localObjectivesList,
                            targetLoIndex + 1, parallelPath);

                    allPaths.addAll(continuedPaths);
                  }
                }
              }

              // If no valid parallel paths found, continue with next LO
              if (allPaths.isEmpty) {
                List<List<String>> continuedPaths =
                    _buildAllPathwayCombinations(
                        localObjectivesList, currentLoIndex + 1, newPath);
                allPaths.addAll(continuedPaths);
              }
            } else {
              // No parallel entries, continue with next LO
              List<List<String>> continuedPaths = _buildAllPathwayCombinations(
                  localObjectivesList, currentLoIndex + 1, newPath);
              allPaths.addAll(continuedPaths);
            }
          }
          break;

        case 'Recursive':
          // Handle recursive pathways - create a loop back
          List<String> recursivePath = List.from(newPath);
          recursivePath.add('(Recursive Loop)');
          allPaths.add(recursivePath);

          // Also continue with next LO for non-recursive flow
          List<List<String>> continuedPaths = _buildAllPathwayCombinations(
              localObjectivesList, currentLoIndex + 1, newPath);
          allPaths.addAll(continuedPaths);
          break;

        default:
          // Default: continue with next LO
          List<List<String>> continuedPaths = _buildAllPathwayCombinations(
              localObjectivesList, currentLoIndex + 1, newPath);
          allPaths.addAll(continuedPaths);
          break;
      }
    } else {
      // No pathway data, continue with next LO
      List<List<String>> continuedPaths = _buildAllPathwayCombinations(
          localObjectivesList, currentLoIndex + 1, newPath);
      allPaths.addAll(continuedPaths);
    }

    return allPaths;
  }

  /// Find the index of a LO by its name or number (from provider logic)
  static int? _findLoIndexByName(
      List<LocalObjectivesList> localObjectivesList, String loName) {
    // First try to find by exact name match
    for (int i = 0; i < localObjectivesList.length; i++) {
      if (localObjectivesList[i].name == loName) {
        return i;
      }
    }

    // Try to find by LO number format (e.g., "LO-2" -> find LO with loNumber 2)
    if (loName.startsWith('LO-')) {
      String numberPart = loName.substring(3);
      try {
        int loNumber = int.parse(numberPart);
        for (int i = 0; i < localObjectivesList.length; i++) {
          if (localObjectivesList[i].loNumber == loNumber) {
            return i;
          }
        }
      } catch (e) {
        // Invalid number format
      }
    }

    return null;
  }
}

// Usage example
